{"version": "0.2.0", "configurations": [{"name": "Launch Chrome", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": false, "runtimeArgs": ["--disable-web-security", "--disable-features=VizDisplayCompositor"]}, {"name": "Launch Firefox", "type": "firefox", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/src", "sourceMaps": true}, {"name": "Attach to Chrome", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}/src", "sourceMaps": true}, {"name": "Debug Vite Dev Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/vite", "args": ["--mode", "development"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "sourceMaps": true}], "compounds": [{"name": "Launch App & Debug", "configurations": ["Debug Vite Dev Server", "Launch Chrome"], "stopAll": true}]}