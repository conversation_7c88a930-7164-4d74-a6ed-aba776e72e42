{"name": "BasicSkeleton", "version": "1.0.0", "description": "基础人体骨架模型配置", "bones": [{"name": "root", "position": [0, 0, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": null}, {"name": "spine", "position": [0, 0.5, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "root"}, {"name": "head", "position": [0, 0.8, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "spine"}, {"name": "leftShoulder", "position": [-0.3, 0.6, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "spine"}, {"name": "rightShoulder", "position": [0.3, 0.6, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "spine"}, {"name": "leftArm", "position": [-0.3, 0.3, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "leftShoulder"}, {"name": "rightArm", "position": [0.3, 0.3, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "rightShoulder"}, {"name": "leftHip", "position": [-0.1, -0.2, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "root"}, {"name": "rightHip", "position": [0.1, -0.2, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "root"}, {"name": "leftLeg", "position": [-0.1, -0.6, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "leftHip"}, {"name": "rightLeg", "position": [0.1, -0.6, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "parent": "rightHip"}], "geometry": [{"name": "torso", "type": "box", "dimensions": [0.4, 0.8, 0.2], "position": [0, 0, 0], "material": {"color": "#4f46e5", "type": "standard"}}, {"name": "head", "type": "sphere", "dimensions": [0.15], "position": [0, 0.5, 0], "material": {"color": "#6366f1", "type": "standard"}}, {"name": "leftArm", "type": "box", "dimensions": [0.1, 0.4, 0.1], "position": [-0.3, 0.2, 0], "rotation": [0, 0, 0.5236], "material": {"color": "#8b5cf6", "type": "standard"}}, {"name": "rightArm", "type": "box", "dimensions": [0.1, 0.4, 0.1], "position": [0.3, 0.2, 0], "rotation": [0, 0, -0.5236], "material": {"color": "#8b5cf6", "type": "standard"}}, {"name": "leftLeg", "type": "box", "dimensions": [0.1, 0.4, 0.1], "position": [-0.1, -0.6, 0], "material": {"color": "#a855f7", "type": "standard"}}, {"name": "rightLeg", "type": "box", "dimensions": [0.1, 0.4, 0.1], "position": [0.1, -0.6, 0], "material": {"color": "#a855f7", "type": "standard"}}, {"name": "directionIndicator", "type": "cone", "dimensions": [0.05, 0.2], "position": [0, 0, 0.15], "material": {"color": "#ef4444", "type": "standard"}}], "animations": {"idle": {"duration": 2000, "keyframes": [{"time": 0, "bones": {"spine": {"rotation": [0, 0, 0]}, "head": {"rotation": [0, 0, 0]}}}, {"time": 1000, "bones": {"spine": {"rotation": [0.1, 0, 0]}, "head": {"rotation": [-0.05, 0, 0]}}}, {"time": 2000, "bones": {"spine": {"rotation": [0, 0, 0]}, "head": {"rotation": [0, 0, 0]}}}]}}, "metadata": {"created": "2024-01-01", "author": "Attitude Tracker 3D", "license": "MIT", "units": "meters", "scale": 1.0}}