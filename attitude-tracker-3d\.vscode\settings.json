{
  // 编辑器配置
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  
  // 文件配置
  "files.eol": "\n",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true
  },
  
  // 搜索配置
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/*.log": true,
    "**/coverage": true
  },
  
  // TypeScript/JavaScript 配置
  "typescript.preferences.importModuleSpecifier": "relative",
  "javascript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "javascript.suggest.autoImports": true,
  
  // ESLint 配置
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": true,
  
  // Prettier 配置
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,
  
  // Emmet 配置
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  },
  
  // 文件关联
  "files.associations": {
    "*.jsx": "javascriptreact",
    "*.tsx": "typescriptreact"
  },
  
  // 终端配置
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  
  // 工作区配置
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
    "*.jsx": "${capture}.js",
    "*.ts": "${capture}.js, ${capture}.d.ts.map, ${capture}.d.ts, ${capture}.js.map",
    "*.tsx": "${capture}.ts",
    "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml"
  },
  
  // Git 配置
  "git.autofetch": true,
  "git.confirmSync": false,
  
  // 扩展配置
  "extensions.ignoreRecommendations": false
}
