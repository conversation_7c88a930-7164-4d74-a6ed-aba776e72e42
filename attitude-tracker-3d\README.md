# Attitude Tracker 3D

一个基于 Web 的三维姿态追踪系统，使用 Three.js 进行骨架模型可视化，前端采用 React + TailwindCSS 实现。

## 功能特性

- 📊 **数据输入**: 支持CSV文件上传或生成示例数据
- 🎮 **3D可视化**: 实时查看姿态数据的3D动画效果  
- 📝 **历史记录**: 管理和回顾之前的数据会话
- 🎯 **交互控制**: 直观的播放控制和视角操作
- ⚡ **实时渲染**: 流畅的动画播放体验

## 技术栈

- **前端框架**: React 18
- **3D渲染**: Three.js + @react-three/fiber + @react-three/drei
- **样式**: TailwindCSS
- **构建工具**: Vite
- **开发语言**: JavaScript (ES6+)

## 项目结构

```
attitude-tracker-3d/
├── public/
│   └── index.html              # HTML模板
├── src/
│   ├── App.jsx                 # 主应用组件
│   ├── main.jsx                # 应用入口
│   ├── index.css               # 全局样式
│   ├── components/             # UI组件
│   │   ├── Header.jsx          # 页面头部
│   │   ├── Sidebar.jsx         # 侧边栏
│   │   └── ControlPanel.jsx    # 控制面板
│   ├── pages/                  # 页面组件
│   │   ├── Home.jsx            # 首页
│   │   ├── DataInput.jsx       # 数据输入页
│   │   ├── Viewer3D.jsx        # 3D查看器
│   │   └── History.jsx         # 历史记录页
│   ├── services/               # 服务模块
│   │   └── dataParser.js       # 数据解析服务
│   ├── store/                  # 状态管理
│   │   └── dataStore.js        # 数据存储
│   └── utils/                  # 工具函数
│       └── playback.js         # 播放控制工具
├── package.json                # 项目配置
├── vite.config.js              # Vite配置
├── tailwind.config.js          # TailwindCSS配置
└── postcss.config.js           # PostCSS配置
```

## 安装和运行

### 前置要求

- Node.js (版本 16 或更高)
- npm 或 yarn

### 安装步骤

1. **克隆或下载项目**
   ```bash
   # 如果是从git克隆
   git clone <repository-url>
   cd attitude-tracker-3d
   ```

2. **安装依赖**
   ```bash
   npm install
   # 或使用 yarn
   yarn install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   # 或使用 yarn
   yarn dev
   ```

4. **打开浏览器**
   
   开发服务器启动后，会自动打开浏览器访问 `http://localhost:3000`

### 构建生产版本

```bash
npm run build
# 或使用 yarn
yarn build
```

构建完成后，生产文件将在 `dist/` 目录中。

## 使用说明

### 数据格式

CSV文件应包含以下列：

- `timestamp`: 时间戳（毫秒）
- `pitch`: 俯仰角（度）
- `yaw`: 偏航角（度）  
- `roll`: 翻滚角（度）
- `x`: X坐标（米）
- `y`: Y坐标（米）
- `z`: Z坐标（米）

示例CSV格式：
```csv
timestamp,pitch,yaw,roll,x,y,z
0,0.0,0.0,0.0,0.0,0.0,1.0
100,5.2,-2.1,1.3,0.1,0.0,1.0
200,10.5,-4.2,2.6,0.2,0.1,1.0
```

### 快捷键

- **数字键 1-4**: 快速切换页面
- **空格键**: 播放/暂停（在3D查看器中）
- **左右箭头**: 上一帧/下一帧（在3D查看器中）
- **鼠标操作**: 
  - 拖拽: 旋转视角
  - 滚轮: 缩放场景
  - 右键拖拽: 平移视角

### 功能说明

1. **数据输入页面**
   - 支持拖拽上传CSV文件
   - 可生成示例数据进行测试
   - 自动验证数据格式

2. **3D查看器**
   - 实时显示姿态动画
   - 显示运动轨迹
   - 提供播放控制面板
   - 侧边栏显示详细信息

3. **历史记录**
   - 管理数据会话（演示功能）
   - 查看会话详情
   - 导出和删除操作

## 开发说明

### 编码规范

- 遵循 fail-fast 原则，避免掩盖错误
- 每个文件不超过 500 行代码
- 低层模块不调用高层模块
- 优先复用已有代码
- 适度的模块化处理

### 架构设计

- **数据层**: `dataStore.js` 负责全局状态管理
- **服务层**: `dataParser.js` 处理数据解析
- **组件层**: 可复用的UI组件
- **页面层**: 业务逻辑页面组件
- **工具层**: 通用工具函数

### 扩展功能

项目为后续功能扩展预留了接口：

- 多人姿态对比
- 视频导出功能
- 流数据支持
- 回放加速控制
- 云端数据存储

## 故障排除

### 常见问题

1. **依赖安装失败**
   - 检查 Node.js 版本是否符合要求
   - 尝试清除缓存: `npm cache clean --force`
   - 删除 `node_modules` 重新安装

2. **3D场景不显示**
   - 检查浏览器是否支持 WebGL
   - 确保已加载数据
   - 查看浏览器控制台错误信息

3. **文件上传失败**
   - 确认文件格式为 CSV
   - 检查文件内容格式是否正确
   - 文件大小不要过大

## 许可证

本项目仅供学习和演示使用。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]

---

**注意**: 这是迭代1版本，部分功能（如历史记录的持久化存储）为演示实现，完整功能将在后续版本中提供。
