/**
 * 控制面板组件
 * 提供播放控制、进度条和播放设置功能
 */

import React, { useState, useEffect } from 'react';
import { 
  getDataLength, 
  getCurrentIndex, 
  setCurrentIndex, 
  getPlayingState, 
  setPlayingState 
} from '../store/dataStore.js';

const ControlPanel = ({ onPlayStateChange, onIndexChange }) => {
  const [currentIndex, setCurrentIndexState] = useState(0);
  const [isPlaying, setIsPlayingState] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const dataLength = getDataLength();

  // 同步状态
  useEffect(() => {
    setCurrentIndexState(getCurrentIndex());
    setIsPlayingState(getPlayingState());
  }, []);

  // 播放/暂停切换
  const togglePlayback = () => {
    const newPlayingState = !isPlaying;
    setIsPlayingState(newPlayingState);
    setPlayingState(newPlayingState);
    onPlayStateChange && onPlayStateChange(newPlayingState);
  };

  // 跳转到指定帧
  const jumpToFrame = (index) => {
    if (index >= 0 && index < dataLength) {
      setCurrentIndexState(index);
      setCurrentIndex(index);
      onIndexChange && onIndexChange(index);
    }
  };

  // 上一帧
  const previousFrame = () => {
    if (currentIndex > 0) {
      jumpToFrame(currentIndex - 1);
    }
  };

  // 下一帧
  const nextFrame = () => {
    if (currentIndex < dataLength - 1) {
      jumpToFrame(currentIndex + 1);
    }
  };

  // 跳转到开始
  const jumpToStart = () => {
    jumpToFrame(0);
  };

  // 跳转到结束
  const jumpToEnd = () => {
    jumpToFrame(dataLength - 1);
  };

  // 进度条变化处理
  const handleProgressChange = (event) => {
    const newIndex = parseInt(event.target.value);
    jumpToFrame(newIndex);
  };

  // 播放速度变化处理
  const handleSpeedChange = (event) => {
    setPlaybackSpeed(parseFloat(event.target.value));
  };

  // 格式化时间显示
  const formatTime = (index) => {
    const minutes = Math.floor(index / 60);
    const seconds = index % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  if (dataLength === 0) {
    return (
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <div className="text-center text-gray-500">
          请先加载数据以使用播放控制
        </div>
      </div>
    );
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg">
      <div className="container mx-auto px-4 py-3">
        {/* 进度条 */}
        <div className="mb-3">
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-500 w-12">
              {formatTime(currentIndex)}
            </span>
            <div className="flex-1">
              <input
                type="range"
                min="0"
                max={dataLength - 1}
                value={currentIndex}
                onChange={handleProgressChange}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
            </div>
            <span className="text-sm text-gray-500 w-12">
              {formatTime(dataLength - 1)}
            </span>
          </div>
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>帧 {currentIndex + 1}</span>
            <span>共 {dataLength} 帧</span>
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="flex items-center justify-center space-x-4">
          {/* 跳转到开始 */}
          <button
            onClick={jumpToStart}
            disabled={currentIndex === 0}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8.445 14.832A1 1 0 0010 14v-2.798l5.445 3.63A1 1 0 0017 14V6a1 1 0 00-1.555-.832L10 8.798V6a1 1 0 00-1.555-.832l-6 4a1 1 0 000 1.664l6 4z" />
            </svg>
          </button>

          {/* 上一帧 */}
          <button
            onClick={previousFrame}
            disabled={currentIndex === 0}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" />
            </svg>
          </button>

          {/* 播放/暂停 */}
          <button
            onClick={togglePlayback}
            className="p-3 rounded-full bg-blue-600 text-white hover:bg-blue-700 transition-colors"
          >
            {isPlaying ? (
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              </svg>
            )}
          </button>

          {/* 下一帧 */}
          <button
            onClick={nextFrame}
            disabled={currentIndex === dataLength - 1}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" />
            </svg>
          </button>

          {/* 跳转到结束 */}
          <button
            onClick={jumpToEnd}
            disabled={currentIndex === dataLength - 1}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4.555 5.168A1 1 0 003 6v8a1 1 0 001.555.832L10 11.202V14a1 1 0 001.555.832l6-4a1 1 0 000-1.664l-6-4A1 1 0 0010 6v2.798l-5.445-3.63z" />
            </svg>
          </button>
        </div>

        {/* 播放设置 */}
        <div className="flex items-center justify-center space-x-6 mt-3 text-sm">
          <div className="flex items-center space-x-2">
            <label className="text-gray-600">播放速度:</label>
            <select
              value={playbackSpeed}
              onChange={handleSpeedChange}
              className="border border-gray-300 rounded px-2 py-1 text-sm"
            >
              <option value={0.25}>0.25x</option>
              <option value={0.5}>0.5x</option>
              <option value={1}>1x</option>
              <option value={1.5}>1.5x</option>
              <option value={2}>2x</option>
              <option value={4}>4x</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ControlPanel;
