import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [react()],

    // 路径别名
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@components': path.resolve(__dirname, './src/components'),
        '@pages': path.resolve(__dirname, './src/pages'),
        '@services': path.resolve(__dirname, './src/services'),
        '@store': path.resolve(__dirname, './src/store'),
        '@utils': path.resolve(__dirname, './src/utils'),
        '@assets': path.resolve(__dirname, './src/assets'),
        '@models': path.resolve(__dirname, './src/models'),
      }
    },

    // 开发服务器配置
    server: {
      port: parseInt(env.VITE_DEV_PORT) || 3000,
      host: env.VITE_DEV_HOST || 'localhost',
      open: env.VITE_DEV_OPEN === 'true',
      hmr: {
        port: parseInt(env.VITE_HMR_PORT) || 24678
      }
    },

    // 构建配置
    build: {
      outDir: 'dist',
      sourcemap: env.VITE_BUILD_SOURCEMAP !== 'false',
      minify: env.VITE_BUILD_MINIFY !== 'false',
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            three: ['three', '@react-three/fiber', '@react-three/drei']
          }
        }
      }
    },

    // 预览服务器配置
    preview: {
      port: 4173,
      host: true
    },

    // 优化配置
    optimizeDeps: {
      include: ['react', 'react-dom', 'three', '@react-three/fiber', '@react-three/drei']
    }
  }
})
