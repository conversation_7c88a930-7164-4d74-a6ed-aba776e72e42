/**
 * 播放控制工具模块
 * 提供动画播放、时间控制和插值计算功能
 */

/**
 * 播放控制器类
 */
export class PlaybackController {
  constructor() {
    this.isPlaying = false;
    this.currentIndex = 0;
    this.playbackSpeed = 1.0;
    this.loop = true;
    this.data = [];
    this.callbacks = {
      onIndexChange: null,
      onPlayStateChange: null,
      onComplete: null
    };
    this.animationId = null;
    this.lastUpdateTime = 0;
    this.frameInterval = 100; // 默认100ms间隔
  }

  /**
   * 设置数据
   * @param {Array} data - PosePoint数组
   */
  setData(data) {
    this.data = [...data];
    this.currentIndex = 0;
    this.stop();
  }

  /**
   * 设置回调函数
   * @param {Object} callbacks - 回调函数对象
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 开始播放
   */
  play() {
    if (this.data.length === 0) return;
    
    this.isPlaying = true;
    this.lastUpdateTime = performance.now();
    this.animate();
    
    if (this.callbacks.onPlayStateChange) {
      this.callbacks.onPlayStateChange(true);
    }
  }

  /**
   * 暂停播放
   */
  pause() {
    this.isPlaying = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    
    if (this.callbacks.onPlayStateChange) {
      this.callbacks.onPlayStateChange(false);
    }
  }

  /**
   * 停止播放
   */
  stop() {
    this.pause();
    this.currentIndex = 0;
    
    if (this.callbacks.onIndexChange) {
      this.callbacks.onIndexChange(0);
    }
  }

  /**
   * 切换播放状态
   */
  toggle() {
    if (this.isPlaying) {
      this.pause();
    } else {
      this.play();
    }
  }

  /**
   * 跳转到指定索引
   * @param {number} index - 目标索引
   */
  seekTo(index) {
    if (index >= 0 && index < this.data.length) {
      this.currentIndex = index;
      
      if (this.callbacks.onIndexChange) {
        this.callbacks.onIndexChange(index);
      }
    }
  }

  /**
   * 设置播放速度
   * @param {number} speed - 播放速度倍数
   */
  setSpeed(speed) {
    this.playbackSpeed = Math.max(0.1, Math.min(10, speed));
    this.frameInterval = 100 / this.playbackSpeed;
  }

  /**
   * 设置循环模式
   * @param {boolean} loop - 是否循环播放
   */
  setLoop(loop) {
    this.loop = loop;
  }

  /**
   * 动画循环
   */
  animate() {
    if (!this.isPlaying) return;

    const currentTime = performance.now();
    const deltaTime = currentTime - this.lastUpdateTime;

    if (deltaTime >= this.frameInterval) {
      this.nextFrame();
      this.lastUpdateTime = currentTime;
    }

    this.animationId = requestAnimationFrame(() => this.animate());
  }

  /**
   * 下一帧
   */
  nextFrame() {
    if (this.data.length === 0) return;

    this.currentIndex++;

    if (this.currentIndex >= this.data.length) {
      if (this.loop) {
        this.currentIndex = 0;
      } else {
        this.currentIndex = this.data.length - 1;
        this.pause();
        
        if (this.callbacks.onComplete) {
          this.callbacks.onComplete();
        }
        return;
      }
    }

    if (this.callbacks.onIndexChange) {
      this.callbacks.onIndexChange(this.currentIndex);
    }
  }

  /**
   * 上一帧
   */
  previousFrame() {
    if (this.data.length === 0) return;

    this.currentIndex--;

    if (this.currentIndex < 0) {
      if (this.loop) {
        this.currentIndex = this.data.length - 1;
      } else {
        this.currentIndex = 0;
      }
    }

    if (this.callbacks.onIndexChange) {
      this.callbacks.onIndexChange(this.currentIndex);
    }
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      isPlaying: this.isPlaying,
      currentIndex: this.currentIndex,
      playbackSpeed: this.playbackSpeed,
      loop: this.loop,
      dataLength: this.data.length,
      progress: this.data.length > 0 ? this.currentIndex / (this.data.length - 1) : 0
    };
  }
}

/**
 * 线性插值函数
 * @param {number} a - 起始值
 * @param {number} b - 结束值
 * @param {number} t - 插值参数 (0-1)
 * @returns {number} 插值结果
 */
export function lerp(a, b, t) {
  return a + (b - a) * t;
}

/**
 * 角度插值（考虑角度的周期性）
 * @param {number} a - 起始角度（度）
 * @param {number} b - 结束角度（度）
 * @param {number} t - 插值参数 (0-1)
 * @returns {number} 插值结果（度）
 */
export function lerpAngle(a, b, t) {
  const diff = ((b - a + 540) % 360) - 180;
  return a + diff * t;
}

/**
 * 姿态数据插值
 * @param {Object} pose1 - 起始姿态
 * @param {Object} pose2 - 结束姿态
 * @param {number} t - 插值参数 (0-1)
 * @returns {Object} 插值后的姿态
 */
export function interpolatePose(pose1, pose2, t) {
  return {
    timestamp: lerp(pose1.timestamp, pose2.timestamp, t),
    pitch: lerpAngle(pose1.pitch, pose2.pitch, t),
    yaw: lerpAngle(pose1.yaw, pose2.yaw, t),
    roll: lerpAngle(pose1.roll, pose2.roll, t),
    x: lerp(pose1.x, pose2.x, t),
    y: lerp(pose1.y, pose2.y, t),
    z: lerp(pose1.z, pose2.z, t)
  };
}

/**
 * 平滑插值（使用缓动函数）
 * @param {number} t - 输入参数 (0-1)
 * @returns {number} 平滑后的参数 (0-1)
 */
export function smoothStep(t) {
  return t * t * (3 - 2 * t);
}

/**
 * 计算两个姿态之间的距离
 * @param {Object} pose1 - 姿态1
 * @param {Object} pose2 - 姿态2
 * @returns {number} 欧几里得距离
 */
export function poseDistance(pose1, pose2) {
  const dx = pose2.x - pose1.x;
  const dy = pose2.y - pose1.y;
  const dz = pose2.z - pose1.z;
  return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * 计算姿态数据的统计信息
 * @param {Array} data - PosePoint数组
 * @returns {Object} 统计信息
 */
export function calculateStats(data) {
  if (data.length === 0) {
    return {
      duration: 0,
      totalDistance: 0,
      averageSpeed: 0,
      bounds: { min: { x: 0, y: 0, z: 0 }, max: { x: 0, y: 0, z: 0 } }
    };
  }

  let totalDistance = 0;
  let minX = data[0].x, maxX = data[0].x;
  let minY = data[0].y, maxY = data[0].y;
  let minZ = data[0].z, maxZ = data[0].z;

  for (let i = 1; i < data.length; i++) {
    // 计算距离
    totalDistance += poseDistance(data[i - 1], data[i]);

    // 更新边界
    minX = Math.min(minX, data[i].x);
    maxX = Math.max(maxX, data[i].x);
    minY = Math.min(minY, data[i].y);
    maxY = Math.max(maxY, data[i].y);
    minZ = Math.min(minZ, data[i].z);
    maxZ = Math.max(maxZ, data[i].z);
  }

  const duration = data[data.length - 1].timestamp - data[0].timestamp;
  const averageSpeed = duration > 0 ? totalDistance / (duration / 1000) : 0;

  return {
    duration,
    totalDistance,
    averageSpeed,
    bounds: {
      min: { x: minX, y: minY, z: minZ },
      max: { x: maxX, y: maxY, z: maxZ }
    }
  };
}

/**
 * 导出播放控制器的默认实例
 */
export const defaultPlaybackController = new PlaybackController();
