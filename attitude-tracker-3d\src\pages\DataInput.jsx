/**
 * 数据输入页面组件
 * 提供文件上传和手动数据输入功能
 */

import React, { useState, useRef } from 'react';
import { parseCSV, generateSampleCSV, validatePoseData } from '../services/dataParser.js';
import { setData } from '../store/dataStore.js';

const DataInput = ({ onNavigate }) => {
  const [inputMethod, setInputMethod] = useState('upload'); // 'upload' 或 'manual'
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  // 处理文件上传
  const handleFileUpload = async (file) => {
    if (!file) return;

    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.csv')) {
      setError('请上传CSV格式的文件');
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const fileContent = await readFileContent(file);
      const posePoints = parseCSV(fileContent);
      
      // 验证数据
      const validation = validatePoseData(posePoints);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // 存储数据
      setData(posePoints);
      setSuccess(`成功加载 ${posePoints.length} 个数据点`);
      
      // 延迟跳转到查看器
      setTimeout(() => {
        onNavigate && onNavigate('/viewer');
      }, 1500);

    } catch (err) {
      setError(`文件解析失败: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 读取文件内容
  const readFileContent = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file);
    });
  };

  // 处理文件选择
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  // 处理拖拽上传
  const handleDragOver = (event) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (event) => {
    event.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (event) => {
    event.preventDefault();
    setDragOver(false);
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  // 生成示例数据
  const handleGenerateSample = () => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const sampleCSV = generateSampleCSV(50); // 生成50个数据点
      const posePoints = parseCSV(sampleCSV);
      
      setData(posePoints);
      setSuccess(`生成了 ${posePoints.length} 个示例数据点`);
      
      setTimeout(() => {
        onNavigate && onNavigate('/viewer');
      }, 1500);

    } catch (err) {
      setError(`示例数据生成失败: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 下载示例CSV
  const handleDownloadSample = () => {
    const sampleCSV = generateSampleCSV(20);
    const blob = new Blob([sampleCSV], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sample_pose_data.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">数据输入</h1>
        <p className="text-gray-600">上传CSV文件或生成示例数据来开始姿态追踪</p>
      </div>

      {/* 输入方式选择 */}
      <div className="flex justify-center mb-8">
        <div className="bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setInputMethod('upload')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              inputMethod === 'upload'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            文件上传
          </button>
          <button
            onClick={() => setInputMethod('manual')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              inputMethod === 'manual'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            示例数据
          </button>
        </div>
      </div>

      {/* 文件上传区域 */}
      {inputMethod === 'upload' && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">上传CSV文件</h2>
          
          {/* 拖拽上传区域 */}
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragOver
                ? 'border-blue-400 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <div className="mb-4">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 48 48"
              >
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <p className="text-lg text-gray-600 mb-2">
              拖拽CSV文件到此处，或
            </p>
            <button
              onClick={() => fileInputRef.current?.click()}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              选择文件
            </button>
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>

          {/* 文件格式说明 */}
          <div className="mt-4 p-4 bg-gray-50 rounded-md">
            <h3 className="text-sm font-medium text-gray-800 mb-2">CSV文件格式要求：</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p>• 包含以下列：timestamp, pitch, yaw, roll, x, y, z</p>
              <p>• timestamp: 时间戳（毫秒）</p>
              <p>• pitch, yaw, roll: 姿态角度（度）</p>
              <p>• x, y, z: 位置坐标（米）</p>
            </div>
          </div>
        </div>
      )}

      {/* 示例数据区域 */}
      {inputMethod === 'manual' && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">示例数据</h2>
          <p className="text-gray-600 mb-6">
            快速生成示例数据来体验姿态追踪功能，或下载示例CSV文件作为参考。
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={handleGenerateSample}
              disabled={isLoading}
              className="flex-1 bg-green-600 text-white py-3 px-6 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? '生成中...' : '生成示例数据'}
            </button>
            <button
              onClick={handleDownloadSample}
              className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors"
            >
              下载示例CSV
            </button>
          </div>
        </div>
      )}

      {/* 状态消息 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="flex">
            <svg className="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
          <div className="flex">
            <svg className="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <p className="text-green-800">{success}</p>
          </div>
        </div>
      )}

      {/* 加载状态 */}
      {isLoading && (
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-gray-600">处理中，请稍候...</p>
        </div>
      )}
    </div>
  );
};

export default DataInput;
