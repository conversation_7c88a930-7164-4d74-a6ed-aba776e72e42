/**
 * 页面头部组件
 * 提供应用标题和导航功能
 */

import React from 'react';

const Header = ({ currentPage, onNavigate }) => {
  const navigationItems = [
    { id: 'home', label: '首页', path: '/' },
    { id: 'input', label: '数据输入', path: '/input' },
    { id: 'viewer', label: '3D查看器', path: '/viewer' },
    { id: 'history', label: '历史记录', path: '/history' }
  ];

  return (
    <header className="bg-blue-600 text-white shadow-lg">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* 应用标题 */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
              <span className="text-blue-600 font-bold text-sm">3D</span>
            </div>
            <h1 className="text-xl font-bold">Attitude Tracker 3D</h1>
          </div>

          {/* 导航菜单 */}
          <nav className="hidden md:flex space-x-6">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => onNavigate && onNavigate(item.path)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                  currentPage === item.id
                    ? 'bg-blue-700 text-white'
                    : 'text-blue-100 hover:bg-blue-500 hover:text-white'
                }`}
              >
                {item.label}
              </button>
            ))}
          </nav>

          {/* 移动端菜单按钮 */}
          <button className="md:hidden p-2 rounded-md hover:bg-blue-500">
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
