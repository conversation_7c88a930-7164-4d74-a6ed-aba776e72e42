/**
 * 全局状态管理模块
 * 用于存储和管理姿态数据
 */

// 数据存储对象
let poseData = [];
let currentIndex = 0;
let isPlaying = false;

/**
 * 存储姿态数据
 * @param {Array} poseList - PosePoint数组
 */
export function setData(poseList) {
  if (!Array.isArray(poseList)) {
    throw new Error('poseList must be an array');
  }
  
  // 验证数据格式
  poseList.forEach((pose, index) => {
    if (!isValidPosePoint(pose)) {
      throw new Error(`Invalid pose data at index ${index}`);
    }
  });
  
  poseData = [...poseList];
  currentIndex = 0;
  console.log(`Stored ${poseData.length} pose points`);
}

/**
 * 获取所有姿态数据
 * @returns {Array} PosePoint数组
 */
export function getData() {
  return [...poseData];
}

/**
 * 获取当前播放索引
 * @returns {number} 当前索引
 */
export function getCurrentIndex() {
  return currentIndex;
}

/**
 * 设置当前播放索引
 * @param {number} index - 索引值
 */
export function setCurrentIndex(index) {
  if (index < 0 || index >= poseData.length) {
    throw new Error('Index out of range');
  }
  currentIndex = index;
}

/**
 * 获取当前姿态点
 * @returns {Object|null} 当前PosePoint或null
 */
export function getCurrentPose() {
  if (currentIndex >= 0 && currentIndex < poseData.length) {
    return poseData[currentIndex];
  }
  return null;
}

/**
 * 获取播放状态
 * @returns {boolean} 是否正在播放
 */
export function getPlayingState() {
  return isPlaying;
}

/**
 * 设置播放状态
 * @param {boolean} playing - 播放状态
 */
export function setPlayingState(playing) {
  isPlaying = Boolean(playing);
}

/**
 * 获取数据长度
 * @returns {number} 数据点数量
 */
export function getDataLength() {
  return poseData.length;
}

/**
 * 清空数据
 */
export function clearData() {
  poseData = [];
  currentIndex = 0;
  isPlaying = false;
}

/**
 * 验证PosePoint数据格式
 * @param {Object} pose - 姿态点数据
 * @returns {boolean} 是否有效
 */
function isValidPosePoint(pose) {
  if (!pose || typeof pose !== 'object') {
    return false;
  }
  
  const requiredFields = ['timestamp', 'pitch', 'yaw', 'roll', 'x', 'y', 'z'];
  
  for (const field of requiredFields) {
    if (!(field in pose) || typeof pose[field] !== 'number') {
      return false;
    }
  }
  
  return true;
}
