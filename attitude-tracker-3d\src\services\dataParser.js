/**
 * 数据解析服务模块
 * 用于解析CSV文件为PosePoint数组
 */

/**
 * 解析CSV文件内容为PosePoint数组
 * @param {string} fileContent - CSV文件内容
 * @returns {Array} PosePoint数组
 */
export function parseCSV(fileContent) {
  if (typeof fileContent !== 'string') {
    throw new Error('File content must be a string');
  }
  
  const lines = fileContent.trim().split('\n');
  
  if (lines.length === 0) {
    throw new Error('Empty file content');
  }
  
  // 检查是否有标题行
  const hasHeader = isHeaderLine(lines[0]);
  const dataLines = hasHeader ? lines.slice(1) : lines;
  
  if (dataLines.length === 0) {
    throw new Error('No data rows found');
  }
  
  const posePoints = [];
  
  dataLines.forEach((line, index) => {
    const lineNumber = hasHeader ? index + 2 : index + 1;
    
    if (line.trim() === '') {
      return; // 跳过空行
    }
    
    try {
      const posePoint = parseLine(line, lineNumber);
      posePoints.push(posePoint);
    } catch (error) {
      throw new Error(`Error parsing line ${lineNumber}: ${error.message}`);
    }
  });
  
  if (posePoints.length === 0) {
    throw new Error('No valid data points found');
  }
  
  // 按时间戳排序
  posePoints.sort((a, b) => a.timestamp - b.timestamp);
  
  console.log(`Parsed ${posePoints.length} pose points from CSV`);
  return posePoints;
}

/**
 * 解析单行数据
 * @param {string} line - CSV行数据
 * @param {number} lineNumber - 行号（用于错误报告）
 * @returns {Object} PosePoint对象
 */
function parseLine(line, lineNumber) {
  const values = line.split(',').map(val => val.trim());
  
  if (values.length < 7) {
    throw new Error(`Expected 7 columns, got ${values.length}`);
  }
  
  const [timestamp, pitch, yaw, roll, x, y, z] = values;
  
  // 转换为数字并验证
  const numericValues = [timestamp, pitch, yaw, roll, x, y, z].map((val, index) => {
    const num = parseFloat(val);
    if (isNaN(num)) {
      const fieldNames = ['timestamp', 'pitch', 'yaw', 'roll', 'x', 'y', 'z'];
      throw new Error(`Invalid ${fieldNames[index]} value: "${val}"`);
    }
    return num;
  });
  
  return {
    timestamp: numericValues[0],
    pitch: numericValues[1],
    yaw: numericValues[2],
    roll: numericValues[3],
    x: numericValues[4],
    y: numericValues[5],
    z: numericValues[6]
  };
}

/**
 * 检查是否为标题行
 * @param {string} line - 第一行内容
 * @returns {boolean} 是否为标题行
 */
function isHeaderLine(line) {
  const values = line.split(',').map(val => val.trim().toLowerCase());
  
  // 检查是否包含常见的标题关键词
  const headerKeywords = ['timestamp', 'time', 'pitch', 'yaw', 'roll', 'x', 'y', 'z'];
  
  return values.some(val => headerKeywords.includes(val));
}

/**
 * 生成示例CSV数据
 * @param {number} count - 数据点数量
 * @returns {string} CSV格式的示例数据
 */
export function generateSampleCSV(count = 10) {
  const lines = ['timestamp,pitch,yaw,roll,x,y,z'];
  
  for (let i = 0; i < count; i++) {
    const timestamp = i * 100; // 每100ms一个数据点
    const pitch = Math.sin(i * 0.1) * 30; // -30到30度
    const yaw = Math.cos(i * 0.15) * 45; // -45到45度
    const roll = Math.sin(i * 0.08) * 20; // -20到20度
    const x = Math.sin(i * 0.05) * 2; // -2到2米
    const y = Math.cos(i * 0.07) * 1.5; // -1.5到1.5米
    const z = 1 + Math.sin(i * 0.03) * 0.5; // 0.5到1.5米
    
    lines.push(`${timestamp},${pitch.toFixed(2)},${yaw.toFixed(2)},${roll.toFixed(2)},${x.toFixed(2)},${y.toFixed(2)},${z.toFixed(2)}`);
  }
  
  return lines.join('\n');
}

/**
 * 验证PosePoint数组的有效性
 * @param {Array} posePoints - PosePoint数组
 * @returns {Object} 验证结果
 */
export function validatePoseData(posePoints) {
  if (!Array.isArray(posePoints)) {
    return { valid: false, error: 'Data must be an array' };
  }
  
  if (posePoints.length === 0) {
    return { valid: false, error: 'Array is empty' };
  }
  
  // 检查时间戳是否递增
  for (let i = 1; i < posePoints.length; i++) {
    if (posePoints[i].timestamp <= posePoints[i - 1].timestamp) {
      return { 
        valid: false, 
        error: `Timestamp not increasing at index ${i}` 
      };
    }
  }
  
  return { valid: true };
}
