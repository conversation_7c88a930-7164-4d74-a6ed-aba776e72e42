/**
 * 模型加载器工具
 * 用于加载和创建3D骨架模型
 */

import * as THREE from 'three';
import skeletonConfig from '../models/skeleton.json';

/**
 * 创建几何体
 * @param {Object} geometryConfig - 几何体配置
 * @returns {THREE.BufferGeometry} Three.js几何体
 */
function createGeometry(geometryConfig) {
  const { type, dimensions } = geometryConfig;
  
  switch (type) {
    case 'box':
      return new THREE.BoxGeometry(...dimensions);
    case 'sphere':
      return new THREE.SphereGeometry(...dimensions);
    case 'cone':
      return new THREE.ConeGeometry(...dimensions);
    case 'cylinder':
      return new THREE.CylinderGeometry(...dimensions);
    default:
      console.warn(`Unknown geometry type: ${type}`);
      return new THREE.BoxGeometry(1, 1, 1);
  }
}

/**
 * 创建材质
 * @param {Object} materialConfig - 材质配置
 * @returns {THREE.Material} Three.js材质
 */
function createMaterial(materialConfig) {
  const { color, type } = materialConfig;
  
  switch (type) {
    case 'standard':
      return new THREE.MeshStandardMaterial({ color });
    case 'basic':
      return new THREE.MeshBasicMaterial({ color });
    case 'phong':
      return new THREE.MeshPhongMaterial({ color });
    case 'lambert':
      return new THREE.MeshLambertMaterial({ color });
    default:
      return new THREE.MeshStandardMaterial({ color });
  }
}

/**
 * 创建骨架模型
 * @param {Object} config - 模型配置（可选，默认使用内置配置）
 * @returns {THREE.Group} 骨架模型组
 */
export function createSkeletonModel(config = skeletonConfig) {
  const skeletonGroup = new THREE.Group();
  skeletonGroup.name = config.name || 'Skeleton';
  
  // 创建几何体部件
  config.geometry.forEach((geometryConfig) => {
    const geometry = createGeometry(geometryConfig);
    const material = createMaterial(geometryConfig.material);
    const mesh = new THREE.Mesh(geometry, material);
    
    // 设置位置
    if (geometryConfig.position) {
      mesh.position.set(...geometryConfig.position);
    }
    
    // 设置旋转
    if (geometryConfig.rotation) {
      mesh.rotation.set(...geometryConfig.rotation);
    }
    
    // 设置缩放
    if (geometryConfig.scale) {
      mesh.scale.set(...geometryConfig.scale);
    }
    
    mesh.name = geometryConfig.name;
    skeletonGroup.add(mesh);
  });
  
  return skeletonGroup;
}

/**
 * 更新骨架姿态
 * @param {THREE.Group} skeletonModel - 骨架模型
 * @param {Object} pose - 姿态数据
 */
export function updateSkeletonPose(skeletonModel, pose) {
  if (!skeletonModel || !pose) return;
  
  // 设置整体位置
  skeletonModel.position.set(pose.x, pose.z, pose.y); // 注意Y和Z轴的转换
  
  // 设置整体旋转（欧拉角转换）
  const euler = new THREE.Euler(
    THREE.MathUtils.degToRad(pose.pitch),
    THREE.MathUtils.degToRad(pose.yaw),
    THREE.MathUtils.degToRad(pose.roll),
    'XYZ'
  );
  skeletonModel.rotation.copy(euler);
}

/**
 * 创建轨迹线
 * @param {Array} poseData - 姿态数据数组
 * @param {Object} options - 选项配置
 * @returns {THREE.Line} 轨迹线对象
 */
export function createTrajectoryLine(poseData, options = {}) {
  const {
    color = '#10b981',
    linewidth = 2,
    opacity = 0.8
  } = options;
  
  if (!poseData || poseData.length === 0) {
    return null;
  }
  
  const points = poseData.map(pose => new THREE.Vector3(pose.x, pose.z, pose.y));
  const geometry = new THREE.BufferGeometry().setFromPoints(points);
  const material = new THREE.LineBasicMaterial({ 
    color, 
    linewidth,
    transparent: true,
    opacity
  });
  
  return new THREE.Line(geometry, material);
}

/**
 * 创建地面网格
 * @param {Object} options - 选项配置
 * @returns {THREE.GridHelper} 网格辅助对象
 */
export function createGroundGrid(options = {}) {
  const {
    size = 20,
    divisions = 20,
    colorCenterLine = '#374151',
    colorGrid = '#6b7280'
  } = options;
  
  return new THREE.GridHelper(size, divisions, colorCenterLine, colorGrid);
}

/**
 * 创建坐标轴辅助
 * @param {number} size - 坐标轴大小
 * @returns {THREE.AxesHelper} 坐标轴辅助对象
 */
export function createAxesHelper(size = 2) {
  return new THREE.AxesHelper(size);
}

/**
 * 加载外部模型文件（预留功能）
 * @param {string} url - 模型文件URL
 * @returns {Promise<THREE.Object3D>} 加载的模型
 */
export async function loadExternalModel(url) {
  // 这里可以使用GLTFLoader等加载器加载外部模型
  // 目前返回默认骨架模型
  console.warn('External model loading not implemented, using default skeleton');
  return createSkeletonModel();
}

/**
 * 获取模型边界框
 * @param {THREE.Object3D} model - 3D模型
 * @returns {THREE.Box3} 边界框
 */
export function getModelBounds(model) {
  const box = new THREE.Box3();
  box.setFromObject(model);
  return box;
}

/**
 * 计算模型中心点
 * @param {THREE.Object3D} model - 3D模型
 * @returns {THREE.Vector3} 中心点
 */
export function getModelCenter(model) {
  const bounds = getModelBounds(model);
  const center = new THREE.Vector3();
  bounds.getCenter(center);
  return center;
}

/**
 * 缩放模型到指定大小
 * @param {THREE.Object3D} model - 3D模型
 * @param {number} targetSize - 目标大小
 */
export function scaleModelToSize(model, targetSize) {
  const bounds = getModelBounds(model);
  const size = bounds.getSize(new THREE.Vector3());
  const maxDimension = Math.max(size.x, size.y, size.z);
  const scale = targetSize / maxDimension;
  model.scale.setScalar(scale);
}

/**
 * 模型配置验证
 * @param {Object} config - 模型配置
 * @returns {boolean} 是否有效
 */
export function validateModelConfig(config) {
  if (!config || typeof config !== 'object') {
    return false;
  }
  
  if (!config.geometry || !Array.isArray(config.geometry)) {
    return false;
  }
  
  return config.geometry.every(geo => 
    geo.type && geo.dimensions && geo.material
  );
}

// 导出默认配置
export { skeletonConfig };
