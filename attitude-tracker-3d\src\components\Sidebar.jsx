/**
 * 侧边栏组件
 * 提供数据信息显示和快速操作功能
 */

import React from 'react';
import { getDataLength, getCurrentIndex, getPlayingState } from '../store/dataStore.js';

const Sidebar = ({ isVisible, onToggle, currentPose }) => {
  const dataLength = getDataLength();
  const currentIndex = getCurrentIndex();
  const isPlaying = getPlayingState();

  return (
    <>
      {/* 侧边栏切换按钮 */}
      <button
        onClick={onToggle}
        className="fixed top-20 left-4 z-50 bg-blue-600 text-white p-2 rounded-md shadow-lg hover:bg-blue-700 transition-colors"
      >
        <svg
          className={`w-5 h-5 transition-transform ${isVisible ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>

      {/* 侧边栏内容 */}
      <div
        className={`fixed top-16 left-0 h-full bg-white shadow-lg transform transition-transform duration-300 z-40 ${
          isVisible ? 'translate-x-0' : '-translate-x-full'
        }`}
        style={{ width: '300px' }}
      >
        <div className="p-4 h-full overflow-y-auto">
          {/* 数据概览 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">数据概览</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">总数据点:</span>
                <span className="font-medium">{dataLength}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">当前索引:</span>
                <span className="font-medium">{currentIndex + 1}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">播放状态:</span>
                <span className={`font-medium ${isPlaying ? 'text-green-600' : 'text-gray-500'}`}>
                  {isPlaying ? '播放中' : '已暂停'}
                </span>
              </div>
            </div>
          </div>

          {/* 当前姿态信息 */}
          {currentPose && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">当前姿态</h3>
              <div className="space-y-3">
                {/* 时间戳 */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="text-xs text-gray-500 mb-1">时间戳</div>
                  <div className="font-mono text-sm">{currentPose.timestamp.toFixed(0)} ms</div>
                </div>

                {/* 姿态角度 */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="text-xs text-gray-500 mb-2">姿态角度 (度)</div>
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div>
                      <div className="text-gray-500">Pitch</div>
                      <div className="font-mono">{currentPose.pitch.toFixed(1)}°</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Yaw</div>
                      <div className="font-mono">{currentPose.yaw.toFixed(1)}°</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Roll</div>
                      <div className="font-mono">{currentPose.roll.toFixed(1)}°</div>
                    </div>
                  </div>
                </div>

                {/* 位置坐标 */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="text-xs text-gray-500 mb-2">位置坐标 (米)</div>
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div>
                      <div className="text-gray-500">X</div>
                      <div className="font-mono">{currentPose.x.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Y</div>
                      <div className="font-mono">{currentPose.y.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Z</div>
                      <div className="font-mono">{currentPose.z.toFixed(2)}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 快速操作 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">快速操作</h3>
            <div className="space-y-2">
              <button className="w-full bg-blue-600 text-white py-2 px-3 rounded-md text-sm hover:bg-blue-700 transition-colors">
                导出截图
              </button>
              <button className="w-full bg-green-600 text-white py-2 px-3 rounded-md text-sm hover:bg-green-700 transition-colors">
                导出数据
              </button>
              <button className="w-full bg-gray-600 text-white py-2 px-3 rounded-md text-sm hover:bg-gray-700 transition-colors">
                重置视角
              </button>
            </div>
          </div>

          {/* 帮助信息 */}
          <div className="text-xs text-gray-500">
            <h4 className="font-medium mb-2">操作提示</h4>
            <ul className="space-y-1">
              <li>• 鼠标拖拽旋转视角</li>
              <li>• 滚轮缩放场景</li>
              <li>• 空格键播放/暂停</li>
              <li>• 左右箭头键切换帧</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 遮罩层 */}
      {isVisible && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-30"
          onClick={onToggle}
        />
      )}
    </>
  );
};

export default Sidebar;
