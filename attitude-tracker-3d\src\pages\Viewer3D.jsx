/**
 * 3D查看器页面组件
 * 使用Three.js实现姿态数据的3D可视化
 */

import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Grid, AxesHelper } from '@react-three/drei';
import Sidebar from '../components/Sidebar.jsx';
import ControlPanel from '../components/ControlPanel.jsx';
import { 
  getData, 
  getCurrentIndex, 
  getCurrentPose, 
  setCurrentIndex, 
  getPlayingState, 
  setPlayingState,
  getDataLength 
} from '../store/dataStore.js';

// 简单的骨架模型组件
const SkeletonModel = ({ pose }) => {
  const meshRef = useRef();

  useEffect(() => {
    if (meshRef.current && pose) {
      // 设置位置
      meshRef.current.position.set(pose.x, pose.z, pose.y); // 注意Y和Z轴的转换
      
      // 设置旋转（欧拉角转换）
      const euler = new THREE.Euler(
        THREE.MathUtils.degToRad(pose.pitch),
        THREE.MathUtils.degToRad(pose.yaw),
        THREE.MathUtils.degToRad(pose.roll),
        'XYZ'
      );
      meshRef.current.rotation.copy(euler);
    }
  }, [pose]);

  return (
    <group ref={meshRef}>
      {/* 主体 */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[0.4, 0.8, 0.2]} />
        <meshStandardMaterial color="#4f46e5" />
      </mesh>
      
      {/* 头部 */}
      <mesh position={[0, 0.5, 0]}>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial color="#6366f1" />
      </mesh>
      
      {/* 左臂 */}
      <mesh position={[-0.3, 0.2, 0]} rotation={[0, 0, Math.PI / 6]}>
        <boxGeometry args={[0.1, 0.4, 0.1]} />
        <meshStandardMaterial color="#8b5cf6" />
      </mesh>
      
      {/* 右臂 */}
      <mesh position={[0.3, 0.2, 0]} rotation={[0, 0, -Math.PI / 6]}>
        <boxGeometry args={[0.1, 0.4, 0.1]} />
        <meshStandardMaterial color="#8b5cf6" />
      </mesh>
      
      {/* 左腿 */}
      <mesh position={[-0.1, -0.6, 0]}>
        <boxGeometry args={[0.1, 0.4, 0.1]} />
        <meshStandardMaterial color="#a855f7" />
      </mesh>
      
      {/* 右腿 */}
      <mesh position={[0.1, -0.6, 0]}>
        <boxGeometry args={[0.1, 0.4, 0.1]} />
        <meshStandardMaterial color="#a855f7" />
      </mesh>
      
      {/* 方向指示器 */}
      <mesh position={[0, 0, 0.15]}>
        <coneGeometry args={[0.05, 0.2]} />
        <meshStandardMaterial color="#ef4444" />
      </mesh>
    </group>
  );
};

// 轨迹线组件
const TrajectoryLine = ({ data }) => {
  const points = data.map(pose => new THREE.Vector3(pose.x, pose.z, pose.y));
  
  return (
    <line>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={points.length}
          array={new Float32Array(points.flatMap(p => [p.x, p.y, p.z]))}
          itemSize={3}
        />
      </bufferGeometry>
      <lineBasicMaterial color="#10b981" linewidth={2} />
    </line>
  );
};

// 动画控制组件
const AnimationController = ({ isPlaying, onIndexChange }) => {
  const dataLength = getDataLength();
  
  useFrame(() => {
    if (isPlaying && dataLength > 0) {
      const currentIndex = getCurrentIndex();
      const nextIndex = (currentIndex + 1) % dataLength;
      
      if (nextIndex !== currentIndex) {
        setCurrentIndex(nextIndex);
        onIndexChange && onIndexChange(nextIndex);
      }
    }
  });

  return null;
};

const Viewer3D = ({ onNavigate }) => {
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [currentPose, setCurrentPose] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  const poseData = getData();

  // 初始化检查
  useEffect(() => {
    if (poseData.length === 0) {
      setError('没有可显示的数据，请先上传数据文件');
      setIsLoading(false);
      return;
    }

    // 设置初始姿态
    setCurrentPose(getCurrentPose());
    setIsLoading(false);
  }, [poseData]);

  // 播放状态变化处理
  const handlePlayStateChange = (playing) => {
    setIsPlaying(playing);
    setPlayingState(playing);
  };

  // 索引变化处理
  const handleIndexChange = (index) => {
    const pose = getCurrentPose();
    setCurrentPose(pose);
  };

  // 键盘控制
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
      }

      switch (event.key) {
        case ' ':
          event.preventDefault();
          handlePlayStateChange(!isPlaying);
          break;
        case 'ArrowLeft':
          event.preventDefault();
          if (getCurrentIndex() > 0) {
            setCurrentIndex(getCurrentIndex() - 1);
            handleIndexChange();
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (getCurrentIndex() < getDataLength() - 1) {
            setCurrentIndex(getCurrentIndex() + 1);
            handleIndexChange();
          }
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isPlaying]);

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-gray-600">加载3D场景中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">无法显示3D场景</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => onNavigate && onNavigate('/input')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            返回数据输入
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full relative">
      {/* 侧边栏 */}
      <Sidebar
        isVisible={sidebarVisible}
        onToggle={() => setSidebarVisible(!sidebarVisible)}
        currentPose={currentPose}
      />

      {/* 3D场景 */}
      <div className="h-full three-container">
        <Canvas
          camera={{ position: [5, 5, 5], fov: 60 }}
          style={{ background: '#f8fafc' }}
        >
          {/* 环境光 */}
          <ambientLight intensity={0.6} />

          {/* 方向光 */}
          <directionalLight
            position={[10, 10, 5]}
            intensity={1}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />

          {/* 网格地面 */}
          <Grid
            args={[20, 20]}
            cellSize={1}
            cellThickness={0.5}
            cellColor="#6b7280"
            sectionSize={5}
            sectionThickness={1}
            sectionColor="#374151"
            fadeDistance={25}
            fadeStrength={1}
            followCamera={false}
            infiniteGrid={true}
          />

          {/* 坐标轴 */}
          <AxesHelper args={[2]} />

          {/* 骨架模型 */}
          {currentPose && <SkeletonModel pose={currentPose} />}

          {/* 轨迹线 */}
          {poseData.length > 1 && <TrajectoryLine data={poseData} />}

          {/* 相机控制 */}
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={50}
            maxPolarAngle={Math.PI / 2}
          />

          {/* 动画控制器 */}
          <AnimationController
            isPlaying={isPlaying}
            onIndexChange={handleIndexChange}
          />
        </Canvas>
      </div>

      {/* 控制面板 */}
      <ControlPanel
        onPlayStateChange={handlePlayStateChange}
        onIndexChange={handleIndexChange}
      />

      {/* 信息显示 */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg">
        <div className="text-sm space-y-1">
          <div className="font-medium text-gray-800">
            数据点: {poseData.length}
          </div>
          <div className="text-gray-600">
            当前: {getCurrentIndex() + 1}
          </div>
          {currentPose && (
            <div className="text-gray-600">
              时间: {currentPose.timestamp.toFixed(0)}ms
            </div>
          )}
        </div>
      </div>

      {/* 帮助提示 */}
      <div className="absolute bottom-20 right-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg">
        <div className="text-xs text-gray-600 space-y-1">
          <div className="font-medium mb-2">快捷键:</div>
          <div>空格: 播放/暂停</div>
          <div>←→: 上一帧/下一帧</div>
          <div>鼠标: 旋转/缩放视角</div>
        </div>
      </div>
    </div>
  );
};

export default Viewer3D;
