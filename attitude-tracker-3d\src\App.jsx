/**
 * 主应用组件
 * 提供路由管理和页面切换功能
 */

import React, { useState, useEffect } from 'react';
import Header from './components/Header.jsx';
import Home from './pages/Home.jsx';
import DataInput from './pages/DataInput.jsx';
import Viewer3D from './pages/Viewer3D.jsx';
import History from './pages/History.jsx';

const App = () => {
  const [currentPage, setCurrentPage] = useState('home');
  const [isLoading, setIsLoading] = useState(false);

  // 页面路由映射
  const routes = {
    '/': 'home',
    '/input': 'input',
    '/viewer': 'viewer',
    '/history': 'history'
  };

  // 页面组件映射
  const pageComponents = {
    home: Home,
    input: DataInput,
    viewer: Viewer3D,
    history: History
  };

  // 导航处理函数
  const handleNavigate = (path) => {
    const pageId = routes[path];
    if (pageId && pageId !== currentPage) {
      setIsLoading(true);
      
      // 模拟页面加载延迟
      setTimeout(() => {
        setCurrentPage(pageId);
        setIsLoading(false);
      }, 100);
    }
  };

  // 获取当前页面组件
  const getCurrentPageComponent = () => {
    const PageComponent = pageComponents[currentPage];
    
    if (!PageComponent) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">页面未找到</h2>
            <p className="text-gray-600">请检查URL或返回首页</p>
            <button
              onClick={() => handleNavigate('/')}
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              返回首页
            </button>
          </div>
        </div>
      );
    }

    return <PageComponent onNavigate={handleNavigate} />;
  };

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyPress = (event) => {
      // 防止在输入框中触发快捷键
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
      }

      switch (event.key) {
        case '1':
          handleNavigate('/');
          break;
        case '2':
          handleNavigate('/input');
          break;
        case '3':
          handleNavigate('/viewer');
          break;
        case '4':
          handleNavigate('/history');
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <Header currentPage={currentPage} onNavigate={handleNavigate} />

      {/* 主要内容区域 */}
      <main className="pt-16 h-screen">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="loading-spinner mx-auto mb-4"></div>
              <p className="text-gray-600">页面加载中...</p>
            </div>
          </div>
        ) : (
          getCurrentPageComponent()
        )}
      </main>

      {/* 全局错误边界提示 */}
      <div id="error-boundary" className="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded-lg shadow-lg max-w-md mx-4">
          <h3 className="text-lg font-semibold text-red-600 mb-2">应用错误</h3>
          <p className="text-gray-600 mb-4">应用遇到了一个错误，请刷新页面重试。</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
          >
            刷新页面
          </button>
        </div>
      </div>
    </div>
  );
};

export default App;
