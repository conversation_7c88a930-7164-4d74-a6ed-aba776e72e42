/**
 * 首页组件
 * 提供应用介绍和快速导航功能
 */

import React from 'react';
import { getDataLength } from '../store/dataStore.js';

const Home = ({ onNavigate }) => {
  const hasData = getDataLength() > 0;

  const features = [
    {
      icon: '📊',
      title: '数据输入',
      description: '支持CSV文件上传或生成示例数据',
      action: () => onNavigate && onNavigate('/input'),
      buttonText: '开始输入'
    },
    {
      icon: '🎮',
      title: '3D可视化',
      description: '实时查看姿态数据的3D动画效果',
      action: () => onNavigate && onNavigate('/viewer'),
      buttonText: hasData ? '查看3D' : '需要数据',
      disabled: !hasData
    },
    {
      icon: '📝',
      title: '历史记录',
      description: '管理和回顾之前的数据会话',
      action: () => onNavigate && onNavigate('/history'),
      buttonText: '查看历史'
    }
  ];

  const quickActions = [
    {
      title: '快速开始',
      description: '使用示例数据立即体验',
      action: () => onNavigate && onNavigate('/input'),
      className: 'bg-blue-600 hover:bg-blue-700 text-white'
    },
    {
      title: '上传数据',
      description: '导入您的CSV数据文件',
      action: () => onNavigate && onNavigate('/input'),
      className: 'bg-green-600 hover:bg-green-700 text-white'
    }
  ];

  return (
    <div className="min-h-full bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-12">
        {/* 头部介绍 */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-800 mb-6">
            Attitude Tracker 3D
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            一个强大的三维姿态追踪可视化工具，支持实时数据展示、轨迹回放和交互式3D场景操作。
            让复杂的姿态数据变得直观易懂。
          </p>
        </div>

        {/* 当前状态 */}
        {hasData && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-12 max-w-2xl mx-auto">
            <div className="flex items-center">
              <svg className="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <p className="text-green-800">
                已加载 {getDataLength()} 个数据点，可以开始3D可视化了！
              </p>
            </div>
          </div>
        )}

        {/* 功能特性 */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300"
            >
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                {feature.description}
              </p>
              <button
                onClick={feature.action}
                disabled={feature.disabled}
                className={`w-full py-3 px-6 rounded-lg font-medium transition-colors duration-200 ${
                  feature.disabled
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {feature.buttonText}
              </button>
            </div>
          ))}
        </div>

        {/* 快速操作 */}
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-8">
            快速操作
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={action.action}
                className={`p-6 rounded-lg text-left transition-colors duration-200 ${action.className}`}
              >
                <h3 className="text-lg font-semibold mb-2">{action.title}</h3>
                <p className="opacity-90">{action.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* 技术特性 */}
        <div className="mt-20 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-12">
            技术特性
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Three.js</h3>
              <p className="text-sm text-gray-600">强大的3D渲染引擎</p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">实时渲染</h3>
              <p className="text-sm text-gray-600">流畅的动画播放</p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">数据导入</h3>
              <p className="text-sm text-gray-600">支持CSV文件格式</p>
            </div>
            <div className="text-center">
              <div className="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">交互控制</h3>
              <p className="text-sm text-gray-600">直观的操作界面</p>
            </div>
          </div>
        </div>

        {/* 底部提示 */}
        <div className="mt-16 text-center text-gray-500">
          <p className="text-sm">
            使用键盘快捷键：1-首页 | 2-数据输入 | 3-3D查看器 | 4-历史记录
          </p>
        </div>
      </div>
    </div>
  );
};

export default Home;
