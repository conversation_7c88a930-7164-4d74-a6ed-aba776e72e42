/**
 * 历史记录页面组件
 * 管理和显示之前的数据会话（预留功能）
 */

import React, { useState } from 'react';
import { getDataLength, getData } from '../store/dataStore.js';

const History = ({ onNavigate }) => {
  const [selectedSession, setSelectedSession] = useState(null);
  
  // 模拟历史会话数据（实际应用中应从本地存储或服务器获取）
  const [sessions] = useState([
    {
      id: 1,
      name: '测试会话 1',
      timestamp: new Date('2024-01-15T10:30:00'),
      dataPoints: 45,
      duration: '2分30秒',
      description: '基础姿态测试数据'
    },
    {
      id: 2,
      name: '运动轨迹记录',
      timestamp: new Date('2024-01-14T15:45:00'),
      dataPoints: 120,
      duration: '5分15秒',
      description: '复杂运动轨迹数据'
    },
    {
      id: 3,
      name: '静态姿态分析',
      timestamp: new Date('2024-01-13T09:20:00'),
      dataPoints: 30,
      duration: '1分45秒',
      description: '静态姿态保持测试'
    }
  ]);

  const currentDataLength = getDataLength();

  // 格式化时间显示
  const formatDate = (date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 加载会话（模拟功能）
  const handleLoadSession = (session) => {
    alert(`加载会话功能暂未实现\n会话: ${session.name}\n数据点: ${session.dataPoints}`);
  };

  // 删除会话（模拟功能）
  const handleDeleteSession = (sessionId) => {
    if (window.confirm('确定要删除这个会话吗？')) {
      alert('删除功能暂未实现');
    }
  };

  // 导出会话（模拟功能）
  const handleExportSession = (session) => {
    alert(`导出会话功能暂未实现\n会话: ${session.name}`);
  };

  // 保存当前会话
  const handleSaveCurrentSession = () => {
    if (currentDataLength === 0) {
      alert('当前没有数据可保存');
      return;
    }
    
    const sessionName = prompt('请输入会话名称:', `会话_${new Date().toLocaleDateString()}`);
    if (sessionName) {
      alert(`保存功能暂未实现\n会话名称: ${sessionName}\n数据点: ${currentDataLength}`);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">历史记录</h1>
        <p className="text-gray-600">管理和回顾之前的数据会话</p>
      </div>

      {/* 当前会话状态 */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">当前会话</h2>
        {currentDataLength > 0 ? (
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600">
                当前加载了 <span className="font-medium text-blue-600">{currentDataLength}</span> 个数据点
              </p>
              <p className="text-sm text-gray-500 mt-1">
                可以保存当前会话以便后续查看
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={handleSaveCurrentSession}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
              >
                保存会话
              </button>
              <button
                onClick={() => onNavigate && onNavigate('/viewer')}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                查看3D
              </button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">当前没有加载数据</p>
            <button
              onClick={() => onNavigate && onNavigate('/input')}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              加载数据
            </button>
          </div>
        )}
      </div>

      {/* 历史会话列表 */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">历史会话</h2>
        </div>
        
        {sessions.length === 0 ? (
          <div className="p-8 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-gray-500">暂无历史会话记录</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {sessions.map((session) => (
              <div
                key={session.id}
                className={`p-6 hover:bg-gray-50 transition-colors ${
                  selectedSession?.id === session.id ? 'bg-blue-50' : ''
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-800">
                        {session.name}
                      </h3>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {session.dataPoints} 数据点
                      </span>
                    </div>
                    <p className="text-gray-600 mt-1">{session.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>📅 {formatDate(session.timestamp)}</span>
                      <span>⏱️ {session.duration}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleLoadSession(session)}
                      className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                    >
                      加载
                    </button>
                    <button
                      onClick={() => handleExportSession(session)}
                      className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                    >
                      导出
                    </button>
                    <button
                      onClick={() => setSelectedSession(session)}
                      className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors"
                    >
                      详情
                    </button>
                    <button
                      onClick={() => handleDeleteSession(session.id)}
                      className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 会话详情模态框 */}
      {selectedSession && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              会话详情
            </h3>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">名称</label>
                <p className="text-gray-800">{selectedSession.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">描述</label>
                <p className="text-gray-800">{selectedSession.description}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">创建时间</label>
                <p className="text-gray-800">{formatDate(selectedSession.timestamp)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">数据点数量</label>
                <p className="text-gray-800">{selectedSession.dataPoints}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">持续时间</label>
                <p className="text-gray-800">{selectedSession.duration}</p>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setSelectedSession(null)}
                className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
              >
                关闭
              </button>
              <button
                onClick={() => {
                  handleLoadSession(selectedSession);
                  setSelectedSession(null);
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                加载会话
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 功能说明 */}
      <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex">
          <svg className="h-5 w-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <div>
            <h3 className="text-sm font-medium text-yellow-800">功能预览</h3>
            <p className="text-sm text-yellow-700 mt-1">
              历史记录功能目前为演示版本。在完整版本中，会话数据将保存到本地存储或云端，支持真实的加载、导出和删除操作。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default History;
